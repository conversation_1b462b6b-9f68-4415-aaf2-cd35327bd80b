/*
 * SPDX-FileCopyrightText: 2010-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */
#include "unity.h"
#include <Arduino.h>
#include <stdio.h>
#include <inttypes.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include "esp_system.h"

#include <SoftwareSerial.h>
#include <ModbusRTUSlave.h>
#include "driver/ledc.h"
#include "driver/rmt.h"
//====== DSHOT协议实现 ======
typedef enum {
    DSHOT600,
    DSHOT300
} dshot_mode_t;

typedef struct {
    int gpio_num;
    rmt_channel_t rmt_channel;
    uint8_t clk_div;
    uint8_t mem_block_num;
    dshot_mode_t mode;
} dshot_rmt_config_t;

void dshot_rmt_init(dshot_rmt_config_t *cfg, dshot_rmt_config_t *config);
void dshot_rmt_send_packet(dshot_rmt_config_t *cfg, uint16_t packet, dshot_mode_t mode);

// 硬件配置
#define MODBUS_BAUDRATE 9600
#define RX_PIN 5
#define TX_PIN 6
#define PWM_PIN 4
#define SLAVE_ID 2
#define REG_ADDR 1 // 寄存器地址

// PWM参数
const int PWM_MIN = 1000;
const int PWM_MAX = 2000;
const int PWM_NEUTRAL = 1500;

// 缓动参数
const float RAMP_STEP = 0.5;
float currentSpeed = 0;
float targetSpeed = 0;

SoftwareSerial softSerial(RX_PIN, TX_PIN);
ModbusRTUSlave modbus(softSerial); // 使用正确的类实例名称

// 寄存器存储数组（库要求必须使用数组）
uint16_t holdingRegs[10] = {0}; 

// ========== esp-fc风格的高精度clamp/map函数 ==========
template<typename T>
T espfc_clamp(const T value, const T min, const T max) {
  if(value > max) return max;
  if(value < min) return min;
  return value;
}

float espfc_map(float x, float in_min, float in_max, float out_min, float out_max) {
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

// ========== EMA滤波相关变量 ==========
float filteredPwm = PWM_NEUTRAL; // 初始为中位
const float alpha = 0.2; // EMA平滑系数，0.1~0.3可调

// ========== 死区处理函数 ==========
float applyDeadband(float value, float band) {
  if (value > band) return value - band;
  else if (value < -band) return value + band;
  return 0;
}

#define PROTOCOL_PWM      0
#define PROTOCOL_DSHOT300 1
#define PROTOCOL_DSHOT600 2

int outputProtocol = PROTOCOL_DSHOT600; // 更改为默认dshot600模，0=PWM, 1=dshot300, 2=dshot600

// ========== DSHOT信号发送函数 ==========
uint16_t createDshotPacket(uint16_t value, bool telemetry) {
  value = value & 0x7FF; // 11位油门
  uint16_t packet = value << 5;
  if (telemetry) packet |= (1 << 4);
  uint16_t csum = 0;
  uint16_t csum_data = packet;
  for (int i = 0; i < 3; i++) {
    csum ^= csum_data;
    csum_data >>= 4;
  }
  csum &= 0xF;
  packet |= csum;
  return packet;
}

// RMT通道配置
#define RMT_TX_CHANNEL RMT_CHANNEL_0
static dshot_rmt_config_t dshot_rmt_cfg;

void sendDshot(uint16_t value, int speed) {
  uint16_t packet = createDshotPacket(value, false);
  dshot_rmt_send_packet(&dshot_rmt_cfg, packet, (speed == 600) ? DSHOT600 : DSHOT300);
}

void setup() {
  Serial.begin(9600);
  Serial.println("==== 电调初始化开始 ====");

  pinMode(PWM_PIN, OUTPUT);

  Serial.println("RMT/DSHOT/PWM配置...");
  Serial.printf("当前输出协议: %d (0=PWM, 1=DSHOT300, 2=DSHOT600)\n", outputProtocol);

  // 初始化RMT配置
  dshot_rmt_config_t rmt_config = {
    .gpio_num = (gpio_num_t)PWM_PIN,
    .rmt_channel = RMT_TX_CHANNEL,
    .clk_div = 80,
    .mem_block_num = 1
  };
  dshot_rmt_init(&dshot_rmt_cfg, &rmt_config);

  // 设置LEDC定时器配置（保留PWM协议）
  ledc_timer_config_t ledc_timer = {
    .speed_mode = LEDC_LOW_SPEED_MODE,
    .duty_resolution = LEDC_TIMER_13_BIT,
    .timer_num = LEDC_TIMER_0,
    .freq_hz = 50,
    .clk_cfg = LEDC_AUTO_CLK
  };
  ledc_timer_config(&ledc_timer);
  
  // 设置LEDC通道配置
  ledc_channel_config_t ledc_channel = {
    .gpio_num = (gpio_num_t)PWM_PIN,
    .speed_mode = LEDC_LOW_SPEED_MODE,
    .channel = LEDC_CHANNEL_0,
    .timer_sel = LEDC_TIMER_0,
    .duty = 0,
    .hpoint = 0
  };
  ledc_channel_config(&ledc_channel);

  Serial.println("输出最小油门信号3秒，等待电调自检...");
  // 初始化时持续输出最小油门信号3秒，确保电调能检测到
  for (int i = 0; i < 150; ++i) { // 150*20ms=3s
    if (outputProtocol == PROTOCOL_PWM) {
      // ESP-IDF LEDC API计算占空比 (13位分辨率，最大值8191)
      uint32_t duty = (uint32_t)((PWM_MIN / 20000.0) * 8191);
      ledc_set_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0, duty);
      ledc_update_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0);
    } else if (outputProtocol == PROTOCOL_DSHOT300) {
      sendDshot(0, 300);
    } else if (outputProtocol == PROTOCOL_DSHOT600) {
      sendDshot(0, 600);
    }
    delay(20);
  }

  Serial.println("Modbus配置...");
  softSerial.begin(MODBUS_BAUDRATE);
  modbus.configureHoldingRegisters(holdingRegs, sizeof(holdingRegs)/sizeof(holdingRegs[0]));
  modbus.begin(SLAVE_ID, MODBUS_BAUDRATE);

  Serial.println("==== 电调初始化完成 ====");
}

//====== DSHOT协议实现 ======
void dshot_rmt_init(dshot_rmt_config_t *cfg, dshot_rmt_config_t *config) {
    uint8_t clk_div = (config->mode == DSHOT600) ? 8 : 16;
    config->mem_block_num = 4;

    rmt_config_t rmt_tx;
    rmt_tx.channel = config->rmt_channel;
    rmt_tx.gpio_num = (gpio_num_t)(config->gpio_num);
    rmt_tx.clk_div = clk_div;
    rmt_tx.mem_block_num = config->mem_block_num;
    rmt_tx.flags = 0;
    rmt_tx.tx_config.loop_en = false;
    rmt_tx.tx_config.carrier_en = false;
    rmt_tx.tx_config.carrier_freq_hz = 0;
    rmt_tx.tx_config.carrier_duty_percent = 0;
    rmt_tx.tx_config.carrier_level = RMT_CARRIER_LEVEL_LOW;
    rmt_tx.tx_config.idle_output_en = true;
    rmt_tx.tx_config.idle_level = RMT_IDLE_LEVEL_LOW;
    ESP_ERROR_CHECK(rmt_config(&rmt_tx));
    ESP_ERROR_CHECK(rmt_driver_install(rmt_tx.channel, 0, 0));
}

void dshot_rmt_send_packet(dshot_rmt_config_t *cfg, uint16_t packet, dshot_mode_t mode) {
    rmt_item32_t items[16] = {0};
    for(uint8_t i = 0; i < 16; i++) {
        items[i].level0 = (packet & 0x8000) ? 1 : 0;
        items[i].duration0 = (mode == DSHOT600) ? 25 : 50;
        items[i].level1 = 0;
        items[i].duration1 = (mode == DSHOT600) ? 50 : 100;
        packet <<= 1;
    }
    rmt_write_items(cfg->rmt_channel, items, 16, true);
    rmt_wait_tx_done(cfg->rmt_channel, portMAX_DELAY);
}

void loop() {
  modbus.poll(); // 处理Modbus请求

  // ========== 调试变量 ==========
  static int lastProtocol = -1;
  static float lastTargetSpeed = -1;

  // 从寄存器数组读取目标速度（保持0-1000范围）
  targetSpeed = constrain(holdingRegs[REG_ADDR], 0, 1000);
  outputProtocol = holdingRegs[2]; // 0=PWM, 1=dshot300, 2=dshot600

  // 协议切换调试
  if (outputProtocol != lastProtocol) {
    Serial.printf("[调试] 输出协议切换: %d -> %d (0=PWM, 1=DSHOT300, 2=DSHOT600)\n", lastProtocol, outputProtocol);
    lastProtocol = outputProtocol;
  }
  // 目标速度突变调试
  if (abs(targetSpeed - lastTargetSpeed) > 10) { // 变化大于10
    Serial.printf("[调试] 目标速度变化: %.1f -> %.1f\n", lastTargetSpeed, targetSpeed);
    lastTargetSpeed = targetSpeed;
  }
  // 打印Modbus寄存器内容
  Serial.print("[调试] Modbus寄存器: ");
  for (int i = 0; i < sizeof(holdingRegs)/sizeof(holdingRegs[0]); ++i) {
    Serial.printf("%d ", holdingRegs[i]);
  }
  Serial.println();

  // 软启动处理（在0-1000范围内）
  float rampStep = RAMP_STEP * 10; // 调整步进值以适应0-1000范围
  if (targetSpeed > currentSpeed) rampStep = 3.0; // 启动更柔和
  else if (targetSpeed < currentSpeed) rampStep = 5.0; // 停止可选更快

  if(abs(targetSpeed - currentSpeed) > 1.0) { // 调整死区
    float step = (targetSpeed > currentSpeed) ? rampStep : -rampStep;
    currentSpeed = constrain(currentSpeed + step, 0, 1000);
  }

  // 速度归一化（1000 → 1.0）
  float normSpeed = currentSpeed / 1000.0f;
  normSpeed = applyDeadband(normSpeed, 0.02);
  normSpeed = espfc_clamp(normSpeed, 0.0f, 1.0f);
  normSpeed = pow(normSpeed, 1.2);
  int pwmValue = (int)espfc_map(normSpeed, 0.0f, 1.0f, PWM_MIN, PWM_MAX);
  pwmValue = espfc_clamp(pwmValue, PWM_MIN, PWM_MAX);
  filteredPwm = alpha * pwmValue + (1 - alpha) * filteredPwm;

  // ========== 协议选择输出 ==========
  if (outputProtocol == PROTOCOL_PWM) {
    uint32_t duty = (uint32_t)((filteredPwm / 20000.0) * 8191);
    ledc_set_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0, duty);
    ledc_update_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0);
  } else if (outputProtocol == PROTOCOL_DSHOT300) {
    sendDshot((uint16_t)(normSpeed * 1999), 300); // Dshot油门范围0~1999
  } else if (outputProtocol == PROTOCOL_DSHOT600) {
    sendDshot((uint16_t)(normSpeed * 1999), 600);
  }

  Serial.printf("目标:%.1f%% 当前:%.1f%% norm:%.3f PWM:%dus(滤波后:%dus) 协议:%d\n", 
                targetSpeed, currentSpeed, normSpeed, pwmValue, (int)filteredPwm, outputProtocol);
  delay(20);
}